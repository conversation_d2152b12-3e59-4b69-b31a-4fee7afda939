# Discord-like Role and Permissions System Implementation Plan

## Overview
This plan outlines the implementation of a comprehensive role and permissions system similar to <PERSON>rd's, featuring hierarchical roles, granular permissions, channel-specific overwrites, and a complete management UI.

## Current System Analysis
- **Existing**: Basic permission system with Users, Agents, and Permissions tables
- **Current Permission Levels**: Read, Write, Admin (simple 3-level system)
- **Current Resources**: Task, User, Agent, System
- **User IDs**: Currently Firebase user IDs, migrating to internal user IDs
- **Missing**: Role-based permissions, hierarchical roles, permission overwrites, comprehensive UI

## 1. Database Schema Changes

### 1.1 New Tables to Create

#### Roles Table
```sql
CREATE TABLE roles (
    id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    color VARCHAR(7), -- Hex color code (#FF0000)
    position INTEGER NOT NULL DEFAULT 0, -- Role hierarchy position
    icon_url TEXT, -- Optional role icon
    is_mentionable BOOLEAN DEFAULT true,
    is_hoisted BOOLEAN DEFAULT false, -- Display separately in member list
    is_managed BOOLEAN DEFAULT false, -- System-managed role (cannot be deleted)
    created_by UUID NOT NULL REFERENCES users(id), -- Internal user ID
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### User_Roles Table (Many-to-Many)
```sql
CREATE TABLE user_roles (
    id UUID PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE, -- Internal user ID
    role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    assigned_by UUID NOT NULL REFERENCES users(id), -- Internal user ID who assigned the role
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, role_id)
);
```

#### Agent_Roles Table (Many-to-Many)
```sql
CREATE TABLE agent_roles (
    id UUID PRIMARY KEY,
    agent_id UUID NOT NULL REFERENCES agents(id) ON DELETE CASCADE,
    role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    assigned_by UUID NOT NULL REFERENCES users(id), -- Internal user ID who assigned the role
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(agent_id, role_id)
);
```

#### Unified Permission Tables with Target Mechanism

##### Task Permissions Table
```sql
CREATE TABLE task_permissions (
    id UUID PRIMARY KEY,
    target_type TEXT NOT NULL CHECK (target_type IN ('role', 'user', 'agent')),
    target_id TEXT NOT NULL, -- Role ID, User ID, or Agent ID
    resource_id UUID REFERENCES tasks(id) ON DELETE CASCADE, -- NULL for global task permissions
    allow_permissions BIGINT NOT NULL DEFAULT 0, -- TaskPermissions bitfield (explicitly allowed)
    deny_permissions BIGINT NOT NULL DEFAULT 0, -- TaskPermissions bitfield (explicitly denied)
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(target_type, target_id, task_id)
);
```

##### User Permissions Table
```sql
CREATE TABLE user_permissions (
    id UUID PRIMARY KEY,
    target_type TEXT NOT NULL CHECK (target_type IN ('role', 'user', 'agent')),
    target_id TEXT NOT NULL, -- Role ID, User ID, or Agent ID
    resource_id UUID REFERENCES users(id) ON DELETE CASCADE, -- NULL for global user permissions
    allow_permissions BIGINT NOT NULL DEFAULT 0, -- UserPermissions bitfield (explicitly allowed)
    deny_permissions BIGINT NOT NULL DEFAULT 0, -- UserPermissions bitfield (explicitly denied)
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(target_type, target_id, target_user_id)
);
```

##### Agent Permissions Table
```sql
CREATE TABLE agent_permissions (
    id UUID PRIMARY KEY,
    target_type TEXT NOT NULL CHECK (target_type IN ('role', 'user', 'agent')),
    target_id TEXT NOT NULL, -- Role ID, User ID, or Agent ID
    resource_id UUID REFERENCES agents(id) ON DELETE CASCADE, -- NULL for global agent permissions
    allow_permissions BIGINT NOT NULL DEFAULT 0, -- AgentPermissions bitfield (explicitly allowed)
    deny_permissions BIGINT NOT NULL DEFAULT 0, -- AgentPermissions bitfield (explicitly denied)
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(target_type, target_id, target_agent_id)
);
```

##### Role Permissions Table
```sql
CREATE TABLE role_permissions (
    id UUID PRIMARY KEY,
    target_type TEXT NOT NULL CHECK (target_type IN ('role', 'user', 'agent')),
    target_id TEXT NOT NULL, -- Role ID, User ID, or Agent ID
    resource_id UUID REFERENCES roles(id) ON DELETE CASCADE, -- NULL for global role permissions
    allow_permissions BIGINT NOT NULL DEFAULT 0, -- RolePermissions bitfield (explicitly allowed)
    deny_permissions BIGINT NOT NULL DEFAULT 0, -- RolePermissions bitfield (explicitly denied)
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(target_type, target_id, target_role_id)
);
```

##### System Permissions Table
```sql
CREATE TABLE system_permissions (
    id UUID PRIMARY KEY,
    target_type TEXT NOT NULL CHECK (target_type IN ('role', 'user', 'agent')),
    target_id TEXT NOT NULL, -- Role ID, User ID, or Agent ID
    allow_permissions BIGINT NOT NULL DEFAULT 0, -- SystemPermissions bitfield (explicitly allowed)
    deny_permissions BIGINT NOT NULL DEFAULT 0, -- SystemPermissions bitfield (explicitly denied)
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(target_type, target_id)
);
```

### 1.2 Default Roles to Create
- **@everyone** (position: 0, default role for all users)
- **Owner** (position: 1000, highest permissions)
- **Admin** (position: 900, administrative permissions)
- **Moderator** (position: 800, moderation permissions)
- **Member** (position: 100, basic member permissions)

## 2. Permission System Design

### 2.1 Permission Flags (Bitfield System per Resource Type)
Each resource type has its own 64-bit permission bitfield, allowing for resource-specific permissions:

```rust
// Task permissions (applies to both regular tasks and projects)
pub struct TaskPermissions;
impl TaskPermissions {
    pub const VIEW: u64 = 1 << 0;
    pub const CREATE: u64 = 1 << 1;
    pub const EDIT: u64 = 1 << 2;
    pub const DELETE: u64 = 1 << 3;
    pub const ASSIGN: u64 = 1 << 4;
    pub const MOVE: u64 = 1 << 5;
    pub const MANAGE_MEMBERS: u64 = 1 << 6; // For projects (root tasks)
    pub const VIEW_DEPENDENCIES: u64 = 1 << 7;
    pub const MANAGE_DEPENDENCIES: u64 = 1 << 8;
    pub const COMMENT: u64 = 1 << 9;
    pub const ATTACH_FILES: u64 = 1 << 10;
}

// User management permissions
pub struct UserPermissions;
impl UserPermissions {
    pub const VIEW: u64 = 1 << 0;
    pub const CREATE: u64 = 1 << 1;
    pub const EDIT: u64 = 1 << 2;
    pub const DELETE: u64 = 1 << 3;
    pub const ASSIGN_ROLES: u64 = 1 << 4;
    pub const REMOVE_ROLES: u64 = 1 << 5;
    pub const VIEW_PROFILE: u64 = 1 << 6;
    pub const EDIT_PROFILE: u64 = 1 << 7;
    pub const MANAGE_PERMISSIONS: u64 = 1 << 8;
}

// Agent management permissions
pub struct AgentPermissions;
impl AgentPermissions {
    pub const VIEW: u64 = 1 << 0;
    pub const CREATE: u64 = 1 << 1;
    pub const EDIT: u64 = 1 << 2;
    pub const DELETE: u64 = 1 << 3;
    pub const ASSIGN_ROLES: u64 = 1 << 4;
    pub const REMOVE_ROLES: u64 = 1 << 5;
    pub const CONFIGURE: u64 = 1 << 6;
    pub const ACTIVATE_DEACTIVATE: u64 = 1 << 7;
    pub const MANAGE_PERMISSIONS: u64 = 1 << 8;
}

// Role management permissions
pub struct RolePermissions;
impl RolePermissions {
    pub const VIEW: u64 = 1 << 0;
    pub const CREATE: u64 = 1 << 1;
    pub const EDIT: u64 = 1 << 2;
    pub const DELETE: u64 = 1 << 3;
    pub const ASSIGN: u64 = 1 << 4;
    pub const REMOVE: u64 = 1 << 5;
    pub const MANAGE_HIERARCHY: u64 = 1 << 6;
    pub const EDIT_PERMISSIONS: u64 = 1 << 7;
    pub const VIEW_MEMBERS: u64 = 1 << 8;
    pub const MANAGE_MEMBERS: u64 = 1 << 9;
}

// System-wide permissions
pub struct SystemPermissions;
impl SystemPermissions {
    pub const VIEW_SETTINGS: u64 = 1 << 0;
    pub const MANAGE_SETTINGS: u64 = 1 << 1;
    pub const VIEW_AUDIT_LOGS: u64 = 1 << 2;
    pub const MANAGE_AUDIT_LOGS: u64 = 1 << 3;
    pub const MANAGE_GLOBAL_PERMISSIONS: u64 = 1 << 4;
    pub const BACKUP_RESTORE: u64 = 1 << 5;
    pub const SYSTEM_MAINTENANCE: u64 = 1 << 6;
    pub const ADMINISTRATOR: u64 = 1 << 63; // Overrides all restrictions globally
}
```

### 2.2 Resource Types
The system supports permissions on the following resource types:

#### Primary Resources (things that need permission control):
- **`task`** - Task management permissions (view, edit, delete, assign, move, etc.)
- **`system`** - Global system-level permissions (creating projects, managing users, etc.)

#### Secondary Resources (entities that can have permissions):
- **`user`** - User management permissions (view profile, edit user, delete user)
- **`agent`** - Agent management permissions (view agent, edit agent, delete agent)
- **`role`** - Role management permissions (create roles, assign roles, etc.)

#### Resource Permission Examples:
- **Task-specific**: Agent can edit Task A but not Task B
- **System-wide**: User can see logs
- **User-specific**: User can edit User A's profile but not User B's
- **Agent-specific**: Role can edit Agent A but not Agent B
- **Role-specific**: User can assign Role X but not Role Y

### 2.3 How Unified Permission Tables Work

#### Permission Storage Architecture
- **Unified Permissions**: Each resource type has one table that handles both base permissions and specific overwrites
- **Target Mechanism**: `target_type` and `target_id` determine who the permission applies to (role, user, or agent)
- **Resource Scoping**: `resource_id` determines scope (NULL = global, specific ID = resource-specific)
- **Allow/Deny System**: Each entry has both `allow_permissions` and `deny_permissions` bitfields

#### Permission Types by Target
```sql
-- Role-based permissions (base permissions for roles)
target_type = 'role', target_id = role_uuid, task_id = NULL

-- User-specific task permissions (overwrites for specific users)
target_type = 'user', target_id = user_uuid, task_id = specific_task_uuid

-- Agent-specific global permissions
target_type = 'agent', target_id = agent_uuid, task_id = NULL
```

#### Permission Resolution Examples
```rust
// Checking if user can edit a specific role
let user_role_permissions = get_permissions::<RolePermissions>(user_id, Some(role_id));
let can_edit = user_role_permissions & RolePermissions::EDIT != 0;
// Checking if role can see a specific agent
let role_agent_permissions = get_permissions::<AgentPermissions>(role_id, Some(agent_id));
let can_edit = user_agent_permissions & AgentPermissions::VIEW != 0;
// Checking if agent can move a specific task
let agent_task_permissions = get_permissions::<TaskPermissions>(agent_id, Some(task_id));
let can_edit = agent_task_permissions & TaskPermissions::MOVE != 0;
```

#### Database Query Examples
```sql
-- Get all task permissions for a user (roles + direct user permissions)
SELECT allow_permissions, deny_permissions
FROM task_permissions
WHERE (target_type = 'role' AND target_id IN (SELECT role_id FROM user_roles WHERE user_id = ?))
   OR (target_type = 'user' AND target_id = ?)
   AND (resource_id = ? OR resource_id IS NULL)
ORDER BY resource_id NULLS LAST; -- Specific permissions override global ones

-- Get effective permissions by combining allow/deny across all entries
```

### 2.4 Permission Resolution Algorithm
Following Discord's model with unified permission tables:

1. **Collect All Applicable Permissions**: Query the resource-specific permission table for:
   - Global role permissions (`target_type = 'role'`, `resource_id = NULL`)
   - Specific role permissions (`target_type = 'role'`, `resource_id = specific_id`)
   - Direct user permissions (`target_type = 'user'`, `target_id = user_id`)
   - Direct agent permissions (`target_type = 'agent'`, `target_id = agent_id`) if applicable

2. **Apply Permission Hierarchy** (in order of precedence):
   - Start with 0 permissions
   - Apply @everyone role global permissions (allow only)
   - Apply other role global permissions (allow only, bitwise OR)
   - Apply @everyone role specific permissions (allow/deny)
   - Apply other role specific permissions (allow/deny)
   - Apply direct user/agent specific permissions (allow/deny) - highest precedence

3. **Permission Calculation**:
   ```rust
   let mut final_permissions = 0u64;

   // Apply allows first
   for permission_entry in applicable_permissions {
       final_permissions |= permission_entry.allow_permissions;
   }

   // Then apply denies (denies override allows)
   for permission_entry in applicable_permissions {
       final_permissions &= !permission_entry.deny_permissions;
   }

   // Administrator permission bypasses all restrictions
   if has_system_administrator_permission(user_id) {
       final_permissions = u64::MAX; // All permissions
   }
   ```

4. **System Administrator Override**: System Administrator permission bypasses all restrictions globally

## 3. Backend Implementation

### 3.1 New Rust Models

#### Core Models
- `Role` struct (simplified, no permission bitfields)
- `UserRole`, `AgentRole` junction models

#### Unified Permission Models
- `TaskPermission` - Unified task permissions (roles, users, agents)
- `UserPermission` - Unified user management permissions
- `AgentPermission` - Unified agent management permissions
- `RolePermission` - Unified role management permissions
- `SystemPermission` - Unified system permissions

#### Permission Model Structure
```rust
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct TaskPermission {
    pub id: Uuid,
    pub target_type: TargetType, // Role, User, Agent
    pub target_id: String,
    pub resource_id: Option<Uuid>, // NULL for global permissions
    pub allow_permissions: u64,
    pub deny_permissions: u64,
    pub created_by: Uuid,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TargetType {
    Role,
    User,
    Agent,
}
```

#### Services
- `PermissionCalculator` service with unified permission resolution
- `TaskPermissionService`, `UserPermissionService`, etc. for resource-specific operations
- Permission helper structs: `TaskPermissions`, `UserPermissions`, `AgentPermissions`, `RolePermissions`, `SystemPermissions`
- `PermissionQuery` helper for building complex permission queries

### 3.2 New API Endpoints

#### Role Management
```
GET    /roles                    # List all roles
POST   /roles                    # Create role
GET    /roles/{id}               # Get role details
PUT    /roles/{id}               # Update role
DELETE /roles/{id}               # Delete role
PUT    /roles/{id}/position      # Update role position
```

#### Role Assignment
```
GET    /users/{id}/roles         # Get user's roles
POST   /users/{id}/roles         # Assign role to user
DELETE /users/{id}/roles/{role_id} # Remove role from user

GET    /agents/{id}/roles        # Get agent's roles
POST   /agents/{id}/roles        # Assign role to agent
DELETE /agents/{id}/roles/{role_id} # Remove role from agent
```

#### Unified Permission Management
```
# Task Permissions (unified - handles both base permissions and overwrites)
GET    /permissions/tasks                           # Get all task permissions
POST   /permissions/tasks                           # Create task permission
GET    /permissions/tasks/{target_type}/{target_id} # Get permissions for specific target
PUT    /permissions/tasks/{target_type}/{target_id} # Set permissions for specific target
DELETE /permissions/tasks/{target_type}/{target_id} # Delete permissions for specific target

# Task-specific permissions
GET    /permissions/tasks/{task_id}                 # Get all permissions for specific task
GET    /permissions/tasks/{task_id}/{target_type}/{target_id} # Get specific target's permissions for task
PUT    /permissions/tasks/{task_id}/{target_type}/{target_id} # Set specific target's permissions for task
DELETE /permissions/tasks/{task_id}/{target_type}/{target_id} # Delete specific target's permissions for task

# User Permissions
GET    /permissions/users                           # Get all user management permissions
POST   /permissions/users                           # Create user permission
GET    /permissions/users/{target_type}/{target_id} # Get permissions for specific target
PUT    /permissions/users/{target_type}/{target_id} # Set permissions for specific target
DELETE /permissions/users/{target_type}/{target_id} # Delete permissions for specific target

# User-specific permissions
GET    /permissions/users/{user_id}                 # Get all permissions for specific user
GET    /permissions/users/{user_id}/{target_type}/{target_id} # Get specific target's permissions for user
PUT    /permissions/users/{user_id}/{target_type}/{target_id} # Set specific target's permissions for user
DELETE /permissions/users/{user_id}/{target_type}/{target_id} # Delete specific target's permissions for user

# Agent Permissions
GET    /permissions/agents                          # Get all agent management permissions
POST   /permissions/agents                          # Create agent permission
GET    /permissions/agents/{target_type}/{target_id} # Get permissions for specific target
PUT    /permissions/agents/{target_type}/{target_id} # Set permissions for specific target
DELETE /permissions/agents/{target_type}/{target_id} # Delete permissions for specific target

# Agent-specific permissions
GET    /permissions/agents/{agent_id}               # Get all permissions for specific agent
GET    /permissions/agents/{agent_id}/{target_type}/{target_id} # Get specific target's permissions for agent
PUT    /permissions/agents/{agent_id}/{target_type}/{target_id} # Set specific target's permissions for agent
DELETE /permissions/agents/{agent_id}/{target_type}/{target_id} # Delete specific target's permissions for agent

# Role Permissions
GET    /permissions/roles                           # Get all role management permissions
POST   /permissions/roles                           # Create role permission
GET    /permissions/roles/{target_type}/{target_id} # Get permissions for specific target
PUT    /permissions/roles/{target_type}/{target_id} # Set permissions for specific target
DELETE /permissions/roles/{target_type}/{target_id} # Delete permissions for specific target

# Role-specific permissions
GET    /permissions/roles/{role_id}                 # Get all permissions for specific role
GET    /permissions/roles/{role_id}/{target_type}/{target_id} # Get specific target's permissions for role
PUT    /permissions/roles/{role_id}/{target_type}/{target_id} # Set specific target's permissions for role
DELETE /permissions/roles/{role_id}/{target_type}/{target_id} # Delete specific target's permissions for role

# System Permissions
GET    /permissions/system                          # Get all system permissions
POST   /permissions/system                          # Create system permission
GET    /permissions/system/{target_type}/{target_id} # Get permissions for specific target
PUT    /permissions/system/{target_type}/{target_id} # Set permissions for specific target
DELETE /permissions/system/{target_type}/{target_id} # Delete permissions for specific target
```

#### Permission Checking
```
GET    /permissions/check/{resource_type}/{resource_id}?permission={permission} # Check specific permission
POST   /permissions/bulk-check                      # Bulk permission checking
GET    /users/{user_id}/effective-permissions/{resource_type}/{resource_id} # Get effective permissions
```

### 3.3 Permission Middleware
- Authentication middleware to extract user/agent from token
- Permission checking middleware for route protection
- Context-aware permission resolution

## 4. Frontend Implementation

### 4.1 New Pages/Components

#### Roles Management Page (`/roles`)
- Role list with hierarchy visualization
- Create/edit role dialog
- Role permission matrix
- Member management per role
- Drag-and-drop role reordering

#### Permission Overwrites Component
- Resource-specific permission management
- Visual permission matrix (Allow/Deny/Inherit)
- Role and user/agent selection
- Real-time permission preview

#### Enhanced Contacts Page
- Role badges on user/agent cards
- Role assignment interface
- Permission summary display

### 4.2 UI Components to Create
- `RoleCard.vue` - Individual role display
- `RoleDialog.vue` - Create/edit role modal
- `PermissionMatrix.vue` - Permission grid interface
- `PermissionOverwriteDialog.vue` - Resource permission management
- `RoleBadge.vue` - Small role indicator
- `MemberRoleManager.vue` - Assign/remove roles interface

### 4.3 Stores to Create/Update
- `rolesStore.ts` - Role management state
- `permissionsStore.ts` - Permission calculation and caching
- Update `usersStore.ts` and `agentsStore.ts` for role integration

## 5. Implementation Phases

### Phase 1: Database and Core Models (Week 1)
- [ ] Create new database tables
- [ ] Implement Rust models and repositories
- [ ] Create default roles and permissions
- [ ] Basic permission calculation service

### Phase 2: Backend API (Week 2)
- [ ] Role CRUD endpoints
- [ ] Role assignment endpoints
- [ ] Permission overwrite endpoints
- [ ] Permission middleware and guards
- [ ] Migration scripts for existing data

### Phase 3: Frontend Core (Week 3)
- [ ] Role management page
- [ ] Basic role assignment interface
- [ ] Permission matrix component
- [ ] Update existing pages with role integration

### Phase 4: Advanced Features (Week 4)
- [ ] Permission overwrites UI
- [ ] Role hierarchy management
- [ ] Advanced permission visualization
- [ ] Audit logging for permission changes

### Phase 5: Testing and Polish (Week 5)
- [ ] Comprehensive testing
- [ ] Performance optimization
- [ ] UI/UX refinements
- [ ] Documentation updates

## 6. Key Features to Implement

### 6.1 Role Hierarchy
- Position-based role ordering
- Higher roles can manage lower roles
- Visual hierarchy in UI
- Drag-and-drop reordering

### 6.2 Permission Inheritance
- @everyone as base role for all users
- Multiple role assignment
- Permission accumulation (bitwise OR)
- Override system for specific resources

### 6.3 Visual Permission Management
- Color-coded permission states (Allow/Deny/Inherit)
- Permission templates for common roles
- Bulk permission assignment
- Permission conflict detection and warnings

### 6.4 Advanced Features
- Role mentioning in tasks/comments
- Role-based task assignment
- Permission audit logs
- Role templates and presets
- Temporary role assignments with expiration

## 7. Security Considerations

### 7.1 Permission Validation
- Server-side permission checks on all operations
- Prevent privilege escalation
- Role hierarchy enforcement
- Administrator permission safeguards

### 7.2 Audit Trail
- Log all permission changes
- Track role assignments/removals
- Monitor administrative actions
- Retention policies for audit logs

## 8. Migration Strategy

### 8.1 Data Migration
- Convert existing permissions to new role system
- Create default roles for existing users
- Preserve existing permission assignments
- Gradual rollout with feature flags

### 8.2 Backward Compatibility
- Maintain existing API endpoints during transition
- Dual permission checking (old + new system)
- Graceful degradation for unsupported features

### 8.3 User ID Migration
- Update all foreign key references from Firebase user IDs to internal user IDs
- Create mapping table for transition period
- Update authentication flow to use internal IDs
- Batch update existing data

## 9. Discord-Inspired UI Features

### 9.1 Role Display (Similar to Discord Screenshots)
- **Role List View**: Hierarchical list showing all roles with member counts
- **Role Colors**: Visual color coding for easy identification
- **Role Icons**: Optional icons for visual distinction
- **Member Count**: Display number of users/agents with each role

### 9.2 Role Management Interface
- **Role Creation Dialog**:
  - Name, description, color picker
  - Icon upload functionality
  - Position in hierarchy
  - Permission matrix with checkboxes
- **Role Editing**:
  - All creation features plus member management
  - Permission inheritance visualization
  - Conflict detection and warnings

### 9.3 Permission Matrix Interface
- **Grid Layout**: Permissions as rows, roles/users as columns
- **Three-State System**: Allow (green ✓), Deny (red ✗), Inherit (gray /)
- **Category Grouping**: Group related permissions together
- **Search and Filter**: Find specific permissions quickly
- **Bulk Operations**: Select multiple permissions for batch changes

### 9.4 Member Role Management
- **User Profile Integration**: Show roles on user profiles
- **Role Assignment Interface**:
  - Search and select roles to assign
  - Visual role hierarchy
  - Permission preview before assignment
- **Role Removal**: Easy removal with confirmation
- **Bulk Role Operations**: Assign/remove roles for multiple users

### 9.5 Resource Permission Overwrites
- **Channel-Style Overwrites**: Similar to Discord's channel permissions
- **Visual Override Indicators**: Show when permissions are overridden
- **Inheritance Chain**: Display how permissions are calculated
- **Override Management**: Easy add/edit/remove overwrites

## 10. Technical Implementation Details

### 10.1 Permission Calculation Performance
- **Caching Strategy**: Cache calculated permissions per user/resource
- **Invalidation**: Smart cache invalidation on role/permission changes
- **Batch Calculations**: Efficient bulk permission checking
- **Database Indexing**: Optimize queries for permission resolution

### 10.2 Real-time Updates
- **WebSocket Integration**: Real-time permission updates
- **Event Broadcasting**: Notify affected users of permission changes
- **UI Reactivity**: Immediate UI updates on permission changes

### 10.3 Error Handling and Validation
- **Permission Validation**: Prevent invalid permission combinations
- **Hierarchy Validation**: Ensure role hierarchy consistency
- **Circular Reference Prevention**: Avoid permission loops
- **Graceful Degradation**: Handle permission calculation failures

## Next Steps
1. **Review and Approval**: Stakeholder review of this comprehensive plan
2. **Environment Setup**: Prepare development environment for implementation
3. **Phase 1 Kickoff**: Begin database schema and core model implementation
4. **Regular Reviews**: Weekly progress reviews and plan adjustments
5. **User Testing**: Early user feedback on UI mockups and prototypes

This plan creates a Discord-like permission system that provides:
- **Hierarchical Role Management**: Clear role hierarchy with position-based ordering
- **Granular Permissions**: Fine-grained control over user capabilities
- **Visual Management Interface**: Intuitive UI for permission management
- **Resource-Specific Overwrites**: Channel-like permission overrides
- **Scalable Architecture**: Efficient permission calculation and caching
- **Security-First Design**: Comprehensive validation and audit trails

The implementation will transform the current basic permission system into a powerful, Discord-inspired role and permission management system that scales with organizational needs while maintaining ease of use.