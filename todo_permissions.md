# Google Zanzibar-Inspired Permission System Implementation Plan

## Overview
This plan outlines the implementation of a comprehensive permission system inspired by Google Zanzibar, featuring tuple-based permissions, unique resource tables, hierarchical roles, and a complete management UI. This approach provides maximum flexibility and scalability for fine-grained access control.

## System Requirements
- **Goal**: Implement a comprehensive Zanzibar-inspired permission system from scratch
- **Target Features**: Tuple-based permissions, hierarchical roles, fine-grained access control
- **Resources**: Task, User, Agent, Role, System namespaces
- **Authentication**: Firebase integration with internal user IDs
- **Architecture**: Clean slate implementation following Google Zanzibar principles

## 1. Database Schema Changes - Zanzibar Approach

### 1.1 Core Resource Tables (Each Resource Gets Its Own Table)

#### Tasks Table (Enhanced)
```sql
CREATE TABLE tasks (
    id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    status VARCHAR(50) NOT NULL DEFAULT 'To Do',
    tags TEXT[],
    assignees UUI<PERSON>[],
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    parent_task_id UUID REFERENCES tasks(id), -- NULL for projects (root tasks)
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### Users Table
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY,
    firebase_id VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    display_name VARCHAR(255),
    photo_url TEXT,
    last_login TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### Agents Table (Enhanced)
```sql
CREATE TABLE agents (
    id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    type VARCHAR(50) NOT NULL,
    configuration JSONB,
    is_active BOOLEAN DEFAULT true,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### Roles Table
```sql
CREATE TABLE roles (
    id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    color VARCHAR(7), -- Hex color code (#FF0000)
    position INTEGER NOT NULL DEFAULT 0, -- Role hierarchy position
    icon_url TEXT, -- Optional role icon
    is_mentionable BOOLEAN DEFAULT true,
    is_hoisted BOOLEAN DEFAULT false, -- Display separately in member list
    is_managed BOOLEAN DEFAULT false, -- System-managed role (cannot be deleted)
    created_by UUID NOT NULL REFERENCES users(id), -- Internal user ID
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 1.2 Relationship Tables (Zanzibar-style Tuples)

#### User_Roles Table (User-Role Relationships)
```sql
CREATE TABLE user_roles (
    id UUID PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    assigned_by UUID NOT NULL REFERENCES users(id),
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, role_id)
);
```

#### Agent_Roles Table (Agent-Role Relationships)
```sql
CREATE TABLE agent_roles (
    id UUID PRIMARY KEY,
    agent_id UUID NOT NULL REFERENCES agents(id) ON DELETE CASCADE,
    role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    assigned_by UUID NOT NULL REFERENCES users(id),
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(agent_id, role_id)
);
```

### 1.3 Permission Tuples Tables (Zanzibar-style)

#### Permission Tuples Table (Core Zanzibar Implementation)
```sql
CREATE TABLE permission_tuples (
    id UUID PRIMARY KEY,
    namespace VARCHAR(50) NOT NULL, -- 'task', 'user', 'agent', 'role', 'system'
    object_id UUID, -- ID of the resource (task, user, agent, role) - NULL for system
    relation VARCHAR(50) NOT NULL, -- 'owner', 'editor', 'viewer', 'member', etc.
    subject_type VARCHAR(50) NOT NULL, -- 'user', 'agent', 'role', 'userset'
    subject_id UUID NOT NULL, -- ID of the subject (user, agent, role)
    subject_relation VARCHAR(50), -- For userset subjects (e.g., 'member' of a role)
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(namespace, object_id, relation, subject_type, subject_id, subject_relation),

    -- Constraints to ensure data integrity
    CHECK (
        (namespace = 'system' AND object_id IS NULL) OR
        (namespace != 'system' AND object_id IS NOT NULL)
    ),
    CHECK (subject_type IN ('user', 'agent', 'role', 'userset'))
);

-- Indexes for performance
CREATE INDEX idx_permission_tuples_namespace_object ON permission_tuples(namespace, object_id);
CREATE INDEX idx_permission_tuples_subject ON permission_tuples(subject_type, subject_id);
CREATE INDEX idx_permission_tuples_relation ON permission_tuples(namespace, relation);
CREATE INDEX idx_permission_tuples_lookup ON permission_tuples(namespace, object_id, relation);

-- Why we keep namespace + subject_type despite UUIDs:
-- 1. Query Performance: Fast filtering without joins
-- 2. Relation Validation: Different namespaces have different valid relations
-- 3. Subject Type Safety: Prevents invalid assignments (e.g., task assigned to task)
-- 4. Schema Flexibility: Supports system-level permissions (object_id = NULL)
-- 5. Zanzibar Compliance: Follows Google's proven architecture
```

#### Permission Definitions Table (Schema Configuration)
```sql
CREATE TABLE permission_definitions (
    id UUID PRIMARY KEY,
    namespace VARCHAR(50) NOT NULL,
    relation VARCHAR(50) NOT NULL,
    definition JSONB NOT NULL, -- Zanzibar-style relation definition
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(namespace, relation)
);
```

#### Example Permission Definitions (Zanzibar Configuration)
```json
-- Task permissions definition
{
  "namespace": "task",
  "relations": {
    "owner": {
      "this": {}
    },
    "editor": {
      "union": {
        "child": [
          {"this": {}},
          {"computed_userset": {"relation": "owner"}}
        ]
      }
    },
    "viewer": {
      "union": {
        "child": [
          {"this": {}},
          {"computed_userset": {"relation": "editor"}},
          {"tuple_to_userset": {
            "tupleset": {"relation": "parent"},
            "computed_userset": {"relation": "viewer"}
          }}
        ]
      }
    },
    "assignee": {
      "this": {}
    },
    "commenter": {
      "union": {
        "child": [
          {"this": {}},
          {"computed_userset": {"relation": "viewer"}}
        ]
      }
    }
  }
}

-- Role permissions definition
{
  "namespace": "role",
  "relations": {
    "member": {
      "this": {}
    },
    "admin": {
      "union": {
        "child": [
          {"this": {}},
          {"computed_userset": {"relation": "member"}}
        ]
      }
    }
  }
}

-- System permissions definition
{
  "namespace": "system",
  "relations": {
    "admin": {
      "this": {}
    },
    "moderator": {
      "union": {
        "child": [
          {"this": {}},
          {"computed_userset": {"relation": "admin"}}
        ]
      }
    },
    "user": {
      "union": {
        "child": [
          {"this": {}},
          {"computed_userset": {"relation": "moderator"}}
        ]
      }
    }
  }
}
```

### 1.4 Default Roles and Initial Tuples
```sql
-- Default roles to create
INSERT INTO roles (id, name, description, position, is_managed) VALUES
('00000000-0000-0000-0000-000000000001', '@everyone', 'Default role for all users', 0, true),
('00000000-0000-0000-0000-000000000002', 'Owner', 'Highest level permissions', 1000, true),
('00000000-0000-0000-0000-000000000003', 'Admin', 'Administrative permissions', 900, true),
('00000000-0000-0000-0000-000000000004', 'Moderator', 'Moderation permissions', 800, true),
('00000000-0000-0000-0000-000000000005', 'Member', 'Basic member permissions', 100, true);

-- Default system permissions (everyone can use the system)
INSERT INTO permission_tuples (namespace, object_id, relation, subject_type, subject_id) VALUES
('system', NULL, 'user', 'role', '00000000-0000-0000-0000-000000000001'),
('system', NULL, 'moderator', 'role', '00000000-0000-0000-0000-000000000004'),
('system', NULL, 'admin', 'role', '00000000-0000-0000-0000-000000000003'),
('system', NULL, 'admin', 'role', '00000000-0000-0000-0000-000000000002');
```

## 2. Zanzibar Permission System Design

### 2.1 Tuple-Based Permission Model
Instead of bitfields, we use Zanzibar's tuple-based approach where permissions are expressed as relationships:

**Tuple Format**: `namespace:object_id#relation@subject_type:subject_id`

**Examples**:
- `task:123e4567-e89b-12d3-a456-426614174000#owner@user:456e7890-e89b-12d3-a456-************`
- `task:123e4567-e89b-12d3-a456-426614174000#viewer@role:00000000-0000-0000-0000-000000000005`
- `role:00000000-0000-0000-0000-000000000003#member@user:456e7890-e89b-12d3-a456-************`

### 2.2 Namespace Definitions
Each resource type (namespace) has its own set of relations:

#### Task Namespace Relations
- **owner**: Full control over the task
- **editor**: Can modify task content and properties
- **viewer**: Can view task details
- **assignee**: Assigned to work on the task
- **commenter**: Can add comments to the task
- **parent**: Parent-child relationship for task hierarchy

#### User Namespace Relations
- **admin**: Can manage user accounts and permissions
- **viewer**: Can view user profiles
- **editor**: Can edit user profiles (self or others)

#### Agent Namespace Relations
- **owner**: Full control over the agent
- **operator**: Can configure and operate the agent
- **viewer**: Can view agent details

#### Role Namespace Relations
- **member**: Has the role assigned
- **admin**: Can manage the role and its members

#### System Namespace Relations
- **admin**: System administrator with full access
- **moderator**: Can moderate content and users
- **user**: Basic system access

### 2.3 Permission Resolution Algorithm (Zanzibar-style)

#### Tuple-Based Permission Checking
The system resolves permissions by traversing the tuple graph and evaluating relation definitions:

```rust
// Example: Check if user can view a task
async fn check_permission(
    user_id: Uuid,
    namespace: &str,
    object_id: Uuid,
    relation: &str
) -> Result<bool, PermissionError> {
    // 1. Direct permission check
    if has_direct_tuple(namespace, object_id, relation, "user", user_id).await? {
        return Ok(true);
    }

    // 2. Check through roles
    let user_roles = get_user_roles(user_id).await?;
    for role_id in user_roles {
        if has_direct_tuple(namespace, object_id, relation, "role", role_id).await? {
            return Ok(true);
        }
    }

    // 3. Check computed usersets (inherited permissions)
    let relation_def = get_relation_definition(namespace, relation).await?;
    if let Some(computed_userset) = relation_def.computed_userset {
        return check_permission(user_id, namespace, object_id, &computed_userset.relation).await;
    }

    // 4. Check tuple-to-userset (hierarchical permissions)
    if let Some(tuple_to_userset) = relation_def.tuple_to_userset {
        let parent_tuples = get_tuples(namespace, object_id, &tuple_to_userset.tupleset.relation).await?;
        for parent_tuple in parent_tuples {
            if check_permission(user_id, namespace, parent_tuple.object_id, &tuple_to_userset.computed_userset.relation).await? {
                return Ok(true);
            }
        }
    }

    Ok(false)
}
```

#### Example Permission Scenarios

**Scenario 1: Direct Task Permission**
```sql
-- User is directly assigned as task viewer
INSERT INTO permission_tuples (namespace, object_id, relation, subject_type, subject_id)
VALUES ('task', 'task-123', 'viewer', 'user', 'user-456');
```

**Scenario 2: Role-Based Permission**
```sql
-- User has a role that grants task viewing
INSERT INTO user_roles (user_id, role_id) VALUES ('user-456', 'role-editor');
INSERT INTO permission_tuples (namespace, object_id, relation, subject_type, subject_id)
VALUES ('task', 'task-123', 'editor', 'role', 'role-editor');
-- Editor role includes viewer permissions via computed_userset
```

**Scenario 3: Hierarchical Permission**
```sql
-- User can view child tasks through parent task permissions
INSERT INTO permission_tuples (namespace, object_id, relation, subject_type, subject_id)
VALUES ('task', 'child-task-789', 'parent', 'task', 'parent-task-123');
INSERT INTO permission_tuples (namespace, object_id, relation, subject_type, subject_id)
VALUES ('task', 'parent-task-123', 'viewer', 'user', 'user-456');
-- Child task inherits viewer permissions from parent via tuple_to_userset
```

### 2.4 Zanzibar APIs Implementation

#### Core API Methods
Following Zanzibar's API design:

```rust
// Read API - Query tuples
async fn read_tuples(
    namespace: &str,
    object_id: Option<Uuid>,
    relation: Option<&str>,
    subject_filter: Option<SubjectFilter>
) -> Result<Vec<PermissionTuple>, ApiError>;

// Write API - Create/Delete tuples
async fn write_tuples(
    writes: Vec<TupleWrite>,
    deletes: Vec<TupleDelete>
) -> Result<(), ApiError>;

// Check API - Verify permissions
async fn check_permission(
    namespace: &str,
    object_id: Uuid,
    relation: &str,
    subject: Subject
) -> Result<bool, ApiError>;

// Expand API - Get all subjects with a relation
async fn expand_relation(
    namespace: &str,
    object_id: Uuid,
    relation: &str
) -> Result<UserSet, ApiError>;

// Watch API - Monitor tuple changes
async fn watch_tuples(
    filter: TupleFilter
) -> Result<impl Stream<Item = TupleChange>, ApiError>;
```

#### Database Queries for Zanzibar Operations

```sql
-- Read tuples with filters
SELECT namespace, object_id, relation, subject_type, subject_id, subject_relation
FROM permission_tuples
WHERE namespace = $1
  AND ($2::UUID IS NULL OR object_id = $2)
  AND ($3::TEXT IS NULL OR relation = $3)
  AND ($4::TEXT IS NULL OR subject_type = $4);

-- Check direct tuple existence
SELECT EXISTS(
    SELECT 1 FROM permission_tuples
    WHERE namespace = $1 AND object_id = $2 AND relation = $3
      AND subject_type = $4 AND subject_id = $5
);

-- Get all subjects for a relation (expand)
SELECT subject_type, subject_id, subject_relation
FROM permission_tuples
WHERE namespace = $1 AND object_id = $2 AND relation = $3;

-- Get user roles for permission checking
SELECT role_id FROM user_roles WHERE user_id = $1;
```

## 3. Backend Implementation - Zanzibar Architecture

### 3.1 New Rust Models

#### Core Zanzibar Models
```rust
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct PermissionTuple {
    pub id: Uuid,
    pub namespace: String,
    pub object_id: Option<Uuid>,
    pub relation: String,
    pub subject_type: SubjectType,
    pub subject_id: Uuid,
    pub subject_relation: Option<String>,
    pub created_by: Uuid,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SubjectType {
    User,
    Agent,
    Role,
    UserSet, // For computed usersets
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Subject {
    pub subject_type: SubjectType,
    pub subject_id: Uuid,
    pub subject_relation: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct PermissionDefinition {
    pub id: Uuid,
    pub namespace: String,
    pub relation: String,
    pub definition: serde_json::Value, // Zanzibar relation definition
    pub created_by: Uuid,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RelationDefinition {
    pub this: Option<DirectUserset>,
    pub computed_userset: Option<ComputedUserset>,
    pub tuple_to_userset: Option<TupleToUserset>,
    pub union: Option<SetOperation>,
    pub intersection: Option<SetOperation>,
    pub exclusion: Option<SetOperation>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComputedUserset {
    pub relation: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TupleToUserset {
    pub tupleset: TupleSet,
    pub computed_userset: ComputedUserset,
}
```

#### Enhanced Resource Models
```rust
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct Task {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub status: String,
    pub tags: Vec<String>,
    pub assignees: Vec<Uuid>,
    pub start_time: Option<DateTime<Utc>>,
    pub end_time: Option<DateTime<Utc>>,
    pub parent_task_id: Option<Uuid>,
    pub created_by: Uuid,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct Role {
    pub id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub color: Option<String>,
    pub position: i32,
    pub icon_url: Option<String>,
    pub is_mentionable: bool,
    pub is_hoisted: bool,
    pub is_managed: bool,
    pub created_by: Uuid,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}
```

#### Zanzibar Services
- `ZanzibarEngine` - Core permission checking engine
- `TupleService` - CRUD operations for permission tuples
- `RelationService` - Manages relation definitions
- `PermissionChecker` - High-level permission checking interface
- `TupleExpander` - Expands relations to find all subjects
- `PermissionWatcher` - Monitors permission changes

### 3.2 Zanzibar API Endpoints

#### Core Zanzibar APIs
```
# Read API - Query tuples
GET    /v1/tuples/read                              # Read permission tuples
POST   /v1/tuples/read                              # Read with complex filters

# Write API - Modify tuples
POST   /v1/tuples/write                             # Write/delete tuples atomically

# Check API - Permission verification
POST   /v1/permissions/check                        # Check if subject has permission

# Expand API - Relation expansion
POST   /v1/permissions/expand                       # Expand relation to get all subjects

# Watch API - Monitor changes
GET    /v1/tuples/watch                             # Watch for tuple changes (SSE)
```

#### Resource Management APIs
```
# Role Management
GET    /v1/roles                                    # List all roles
POST   /v1/roles                                    # Create role
GET    /v1/roles/{id}                               # Get role details
PUT    /v1/roles/{id}                               # Update role
DELETE /v1/roles/{id}                               # Delete role
PUT    /v1/roles/{id}/position                      # Update role position

# Role Assignment (via tuples)
POST   /v1/roles/{role_id}/members                  # Add member to role
DELETE /v1/roles/{role_id}/members/{user_id}        # Remove member from role
GET    /v1/roles/{role_id}/members                  # List role members

# Task Management with Permissions
GET    /v1/tasks                                    # List accessible tasks
POST   /v1/tasks                                    # Create task (auto-assigns owner)
GET    /v1/tasks/{id}                               # Get task details
PUT    /v1/tasks/{id}                               # Update task
DELETE /v1/tasks/{id}                               # Delete task

# Task Permission Management
POST   /v1/tasks/{task_id}/permissions              # Grant task permissions
DELETE /v1/tasks/{task_id}/permissions              # Revoke task permissions
GET    /v1/tasks/{task_id}/permissions              # List task permissions
```

#### High-Level Permission APIs
```
# Permission Checking
POST   /v1/permissions/check                        # Check single permission
POST   /v1/permissions/batch-check                  # Check multiple permissions
GET    /v1/users/{user_id}/permissions/{namespace}  # Get user's permissions in namespace

# Permission Management
POST   /v1/permissions/grant                        # Grant permission (creates tuple)
POST   /v1/permissions/revoke                       # Revoke permission (deletes tuple)
GET    /v1/permissions/list                         # List permissions for resource

# Relation Schema Management
GET    /v1/schema/namespaces                        # List all namespaces
GET    /v1/schema/namespaces/{namespace}            # Get namespace definition
PUT    /v1/schema/namespaces/{namespace}            # Update namespace definition
```

#### Example API Requests
```json
// Check if user can edit a task
POST /v1/permissions/check
{
  "namespace": "task",
  "object_id": "123e4567-e89b-12d3-a456-426614174000",
  "relation": "editor",
  "subject": {
    "subject_type": "user",
    "subject_id": "456e7890-e89b-12d3-a456-************"
  }
}

// Grant task viewer permission to a role
POST /v1/tuples/write
{
  "writes": [
    {
      "namespace": "task",
      "object_id": "123e4567-e89b-12d3-a456-426614174000",
      "relation": "viewer",
      "subject": {
        "subject_type": "role",
        "subject_id": "00000000-0000-0000-0000-000000000005"
      }
    }
  ]
}

// Expand task viewers to see all users/roles with access
POST /v1/permissions/expand
{
  "namespace": "task",
  "object_id": "123e4567-e89b-12d3-a456-426614174000",
  "relation": "viewer"
}
```

### 3.3 Zanzibar Permission Middleware
```rust
// Permission checking middleware
pub async fn check_permission_middleware(
    req: Request<Body>,
    next: Next<Body>,
) -> Result<Response, StatusCode> {
    let user_id = extract_user_from_token(&req)?;
    let required_permission = extract_required_permission(&req)?;

    if let Some((namespace, object_id, relation)) = required_permission {
        let has_permission = zanzibar_engine
            .check_permission(user_id, &namespace, object_id, &relation)
            .await
            .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

        if !has_permission {
            return Err(StatusCode::FORBIDDEN);
        }
    }

    Ok(next.run(req).await)
}

// Context-aware permission resolution
pub struct PermissionContext {
    pub user_id: Uuid,
    pub user_roles: Vec<Uuid>,
    pub cached_permissions: HashMap<String, bool>,
}
```

## 4. Frontend Implementation - Zanzibar Integration

### 4.1 New Pages/Components

#### Roles Management Page (`/roles`)
- Role list with hierarchy visualization
- Create/edit role dialog
- Tuple-based permission assignment interface
- Member management per role with real-time updates
- Drag-and-drop role reordering

#### Permission Management Component
- Zanzibar tuple visualization
- Resource-specific permission management
- Relation-based permission matrix
- Real-time permission checking and preview
- Tuple creation/deletion interface

#### Enhanced Contacts Page
- Role badges on user/agent cards
- Tuple-based role assignment interface
- Permission summary with relation expansion
- Real-time permission status updates

### 4.2 UI Components to Create
- `ZanzibarTupleViewer.vue` - Visualize permission tuples
- `RelationMatrix.vue` - Show relations between resources
- `PermissionChecker.vue` - Real-time permission checking
- `TupleEditor.vue` - Create/edit permission tuples
- `RoleHierarchy.vue` - Visual role hierarchy display
- `PermissionInheritance.vue` - Show permission inheritance chains

### 4.3 Stores to Create/Update
- `zanzibarStore.ts` - Core Zanzibar operations and caching
- `tuplesStore.ts` - Permission tuple management
- `relationsStore.ts` - Relation definitions and schema
- `permissionCheckerStore.ts` - Real-time permission checking
- Update existing stores to use Zanzibar permission checks

## 5. Implementation Phases - Zanzibar Migration

### Phase 1: Core Zanzibar Infrastructure (Week 1)
- [ ] Create Zanzibar database tables (permission_tuples, permission_definitions)
- [ ] Implement core Zanzibar models and repositories
- [ ] Build basic tuple CRUD operations
- [ ] Create relation definition system
- [ ] Implement basic permission checking engine

### Phase 2: Zanzibar APIs and Services (Week 2)
- [ ] Implement core Zanzibar APIs (Read, Write, Check, Expand, Watch)
- [ ] Build permission checking middleware
- [ ] Create tuple management services
- [ ] Implement relation expansion algorithms
- [ ] Add caching layer for performance

### Phase 3: Resource Integration (Week 3)
- [ ] Implement task, user, agent, role models with Zanzibar integration
- [ ] Create resource management endpoints with permission checks
- [ ] Set up default roles and initial tuples
- [ ] Implement authentication flow with Firebase integration

### Phase 4: Frontend Zanzibar Integration (Week 4)
- [ ] Build Zanzibar stores and services
- [ ] Create tuple visualization components
- [ ] Update permission checking throughout UI
- [ ] Implement real-time permission updates
- [ ] Add relation-based permission management

### Phase 5: Advanced Features and Optimization (Week 5)
- [ ] Implement permission inheritance visualization
- [ ] Add bulk permission operations
- [ ] Optimize performance with advanced caching
- [ ] Add comprehensive audit logging
- [ ] Performance testing and optimization

## 6. Key Zanzibar Features to Implement

### 6.1 Tuple-Based Permission Model
- Relational tuple storage and management
- Namespace-based resource organization
- Subject-relation-object permission model
- Real-time tuple synchronization

### 6.2 Relation Definition System
- Configurable relation schemas per namespace
- Computed userset support (inheritance)
- Tuple-to-userset support (hierarchical permissions)
- Union, intersection, and exclusion operations

### 6.3 Advanced Permission Resolution
- Graph-based permission traversal
- Cached permission checking for performance
- Recursive relation expansion
- Conflict resolution with precedence rules

### 6.4 Real-Time Permission Management
- Live permission checking and updates
- Tuple change notifications
- Permission inheritance visualization
- Bulk tuple operations with atomic transactions

### 6.5 Zanzibar-Specific Features
- Permission expansion to show all subjects
- Relation debugging and visualization
- Performance monitoring and optimization
- Schema validation and migration tools

## 7. Security Considerations - Zanzibar

### 7.1 Tuple Validation and Security
- Server-side tuple validation on all operations
- Prevent unauthorized tuple creation/deletion
- Namespace isolation and access control
- Subject verification and authentication

### 7.2 Permission Checking Security
- Atomic permission checking operations
- Cache invalidation on tuple changes
- Protection against permission enumeration attacks
- Rate limiting on permission checks

### 7.3 Audit and Monitoring
- Comprehensive tuple change logging
- Permission check audit trails
- Anomaly detection for unusual permission patterns
- Real-time security monitoring and alerts

## 8. Initial Setup and Configuration

### 8.1 Database Initialization
- Create all Zanzibar tables and indexes
- Set up default roles and system permissions
- Configure initial namespace definitions
- Establish baseline security policies

### 8.2 Authentication Setup
- Configure Firebase authentication integration
- Set up automatic user creation on first login
- Implement session management with internal user IDs
- Configure default role assignment for new users

### 8.3 Performance Configuration
- Implement tuple caching strategies from day one
- Configure optimal database indexes for tuple queries
- Set up monitoring for permission check latency
- Plan for horizontal scaling architecture

### 8.4 Security and Validation
- Implement tuple validation rules
- Set up audit logging for all permission changes
- Configure rate limiting for permission checks
- Establish security monitoring and alerting

## 9. Zanzibar-Inspired UI Features

### 9.1 Tuple Visualization Interface
- **Tuple Graph View**: Visual representation of permission relationships
- **Namespace Explorer**: Browse permissions by resource type
- **Relation Mapping**: Show how relations connect subjects and objects
- **Real-time Updates**: Live tuple changes and notifications

### 9.2 Permission Checking Interface
- **Permission Debugger**: Step-through permission resolution
- **Relation Expansion**: Visualize all subjects with a relation
- **Inheritance Chains**: Show how permissions are inherited
- **Performance Metrics**: Display permission check latency

### 9.3 Advanced Permission Management
- **Tuple Editor**: Create/edit permission tuples with validation
- **Bulk Operations**: Mass tuple creation/deletion with preview
- **Schema Management**: Configure relation definitions
- **Migration Tools**: Convert between permission formats

### 9.4 Role and Resource Integration
- **Enhanced Role Display**: Show tuple-based role memberships
- **Resource Permission Views**: Display all permissions for a resource
- **Subject Permission Summary**: Show all permissions for a user/agent
- **Cross-Reference Tools**: Find related permissions across namespaces

### 9.5 Monitoring and Analytics
- **Permission Usage Analytics**: Track most-used permissions
- **Tuple Change History**: Audit trail with detailed changes
- **Performance Dashboard**: Monitor Zanzibar system health
- **Security Alerts**: Detect unusual permission patterns

## 10. Technical Implementation Details - Zanzibar

### 10.1 Zanzibar Performance Optimization
- **Tuple Caching**: Multi-level caching for tuples and computed permissions
- **Index Optimization**: Specialized indexes for tuple queries and joins
- **Batch Operations**: Efficient bulk tuple reads and writes
- **Query Optimization**: Minimize database roundtrips for permission checks

### 10.2 Scalability and Reliability
- **Horizontal Scaling**: Distribute tuple storage across multiple nodes
- **Replication**: Multi-region tuple replication for global access
- **Consistency**: Ensure tuple consistency across distributed systems
- **Failover**: Graceful degradation when Zanzibar services are unavailable

### 10.3 Monitoring and Observability
- **Metrics Collection**: Track permission check latency and throughput
- **Distributed Tracing**: Trace permission checks across services
- **Health Monitoring**: Monitor Zanzibar system health and performance
- **Alerting**: Real-time alerts for permission system issues

### 10.4 Development and Testing
- **Tuple Testing**: Comprehensive test suites for permission scenarios
- **Performance Testing**: Load testing for permission check scalability
- **Schema Validation**: Automated validation of relation definitions
- **Integration Testing**: End-to-end testing of permission flows

## Next Steps - Zanzibar Implementation
1. **Zanzibar Architecture Review**: Deep dive into Google Zanzibar paper and implementations
2. **Database Schema Setup**: Create all tables, indexes, and constraints
3. **Core Engine Development**: Build the fundamental Zanzibar permission checking engine
4. **API Development**: Implement the core Zanzibar APIs (Read, Write, Check, Expand)
5. **Team Training**: Educate team on Zanzibar concepts and implementation patterns

## Benefits of Zanzibar Approach
This Zanzibar-inspired permission system provides:
- **Maximum Flexibility**: Tuple-based model supports any permission scenario
- **Scalable Architecture**: Designed to handle millions of permission checks
- **Fine-Grained Control**: Relation-based permissions with inheritance
- **Real-Time Updates**: Live permission changes with immediate effect
- **Future-Proof Design**: Extensible model that grows with requirements
- **Industry Standard**: Based on Google's proven authorization system

The implementation will create a world-class, Zanzibar-inspired authorization system from the ground up that can scale to enterprise levels while providing the flexibility needed for complex permission scenarios.